[project]
name = "tv-datafeed"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "pandas>=2.3.2",
    "requests>=2.32.5",
    "websocket-client>=1.8.0",
]


[build-system]
requires = ["uv_build>=0.8.19"]
build-backend = "uv_build"

[tool.uv.build-backend]
module-name="tv_data_feed"
module-root=""

[tool.uv]
link-mode = 'symlink'
