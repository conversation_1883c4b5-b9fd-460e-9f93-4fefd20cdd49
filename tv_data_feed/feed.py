import datetime  # noqa: I001
import enum
import json
import logging
import random
import re
import string
import pandas as pd
import requests
from websocket import create_connection

logger = logging.getLogger(__name__)


class Interval(enum.Enum):
    M1 = "1"
    M3 = "3"
    M5 = "5"
    M15 = "15"
    M30 = "30"
    M45 = "45"
    H1 = "1H"
    H2 = "2H"
    H3 = "3H"
    H4 = "4H"
    D1 = "1D"
    W1 = "1W"
    MO1 = "1M"


class TradingviewClient:
    __sign_in_url = "https://www.tradingview.com/accounts/signin/"
    __search_url = (
        # "https://symbol-search.tradingview.com/symbol_search/?text={}&hl=1&exchange={}&lang=en&type=&domain=production"
        "https://symbol-search.tradingview.com/symbol_search/v3/?text={}&hl=1&exchange={}&lang=en&search_type={}&domain=production{}{}"
    )
    __search_headers = {"origin": "https://www.tradingview.com"}
    __ws_headers = json.dumps({"Origin": "https://data.tradingview.com"})
    __signin_headers = {"Referer": "https://www.tradingview.com"}
    __ws_timeout = 5

    def __init__(
        self,
        username: str | None = None,
        password: str | None = None,
    ) -> None:
        """Create TradingviewClient object

        Message example:
        ~m~115~m~{"m":"resolve_symbol","p":["cs_fawjefjawekf","symbol_1"={"symbol:"SPX","adjustment":"splits","session":"regular"}]}
        ~m~65~m~{"m":"create_series","p":["cs_fawjefjawekf","s1","s1","1D",2000]}

        Args:
            username (str, optional): tradingview username. Defaults to None.
            password (str, optional): tradingview password. Defaults to None.
        """
        self.ws_debug = False

        self.token = self.__auth(username, password)

        if self.token is None:
            # ログインしない場合データ取得に制限
            self.token = "unauthorized_user_token"
            # logger.warning(
            #     "you are using nologin method, data you access may be limited"
            # )

        self.ws = None
        self.session = self.__generate_session()
        self.chart_session = self.__generate_chart_session()

    def __auth(self, username, password):
        if username is None or password is None:
            token = None

        else:
            data = {"username": username, "password": password, "remember": "on"}
            try:
                response = requests.post(
                    url=self.__sign_in_url, data=data, headers=self.__signin_headers
                )
                token = response.json()["user"]["auth_token"]
            except Exception as e:
                logger.error("error while signin", exc_info=e)
                token = None

        return token

    def ___create_connection(self):
        logging.debug("creating websocket connection")
        self.ws = create_connection(
            "wss://data.tradingview.com/socket.io/websocket",
            headers=self.__ws_headers,
            timeout=self.__ws_timeout,
        )

    def __create_connection(self):
        logging.debug("creating websocket connection")
        try:
            self.ws = create_connection(
                "wss://data.tradingview.com/socket.io/websocket",
                headers=self.__ws_headers,
                timeout=self.__ws_timeout,
            )
            if self.ws is None:
                raise ConnectionError("WebSocket connection returned None")
            logging.debug("websocket connection created successfully")
        except Exception as e:
            logging.error(f"Failed to create websocket connection: {e}")
            self.ws = None
            raise ConnectionError(f"Could not establish websocket connection: {e}")

    @staticmethod
    def __filter_raw_message(text):
        try:
            p = re.search('"m":"(.+?)",', text)
            found = p.group(1) if p else None
            p2 = re.search('"p":(.+?"}"])}', text)
            found2 = p2.group(1) if p2 else None

            return found, found2
        except AttributeError:
            logger.error("error in filter_raw_message")

    @staticmethod
    def __generate_session():
        stringLength = 12
        letters = string.ascii_lowercase
        random_string = "".join(random.choice(letters) for i in range(stringLength))
        return "qs_" + random_string

    @staticmethod
    def __generate_chart_session():
        stringLength = 12
        letters = string.ascii_lowercase
        random_string = "".join(random.choice(letters) for i in range(stringLength))
        return "cs_" + random_string

    @staticmethod
    def __prepend_header(st):
        return "~m~" + str(len(st)) + "~m~" + st

    @staticmethod
    def __construct_message(func, param_list):
        return json.dumps({"m": func, "p": param_list}, separators=(",", ":"))

    def __create_message(self, func, paramList):
        return self.__prepend_header(self.__construct_message(func, paramList))

    def __send_message(self, func, args):
        m = self.__create_message(func, args)
        if self.ws_debug:
            print(m)

        if self.ws is None:
            self.__create_connection()
            print("created new connection")
            assert self.ws is not None, "WebSocket connection is None"
        self.ws.send(m)

    @staticmethod
    def __create_df(raw_data, symbol):
        try:
            out = re.search(r'"s":\[(.+?)\}\]', raw_data)
            assert out is not None, "No data found"
            out = out.group(1)
            x = out.split(',{"')
            data = list()
            volume_data = True

            for xi in x:
                xi = re.split(r"\[|:|,|\]", xi)
                ts = datetime.datetime.fromtimestamp(float(xi[4]))

                row: list[datetime.datetime | float] = [ts]

                for i in range(5, 10):
                    # skip converting volume data if does not exists
                    if not volume_data and i == 9:
                        row.append(0.0)
                        continue
                    try:
                        row.append(float(xi[i]))

                    except ValueError:
                        volume_data = False
                        row.append(0.0)
                        logger.debug("no volume data")

                data.append(row)

            data = pd.DataFrame(
                data, columns=["datetime", "open", "high", "low", "close", "volume"]
            ).set_index("datetime")
            data.insert(0, "symbol", value=symbol)
            return data
        except AttributeError:
            logger.error("no data, please check the exchange and symbol")

    @staticmethod
    def __format_symbol(symbol, exchange, contract: int | None = None):
        if ":" in symbol:
            pass
        elif contract is None:
            symbol = f"{exchange}:{symbol}"

        elif isinstance(contract, int):
            symbol = f"{exchange}:{symbol}{contract}!"

        else:
            raise ValueError("not a valid contract")

        return symbol

    def get_hist(
        self,
        symbol: str,
        exchange: str = "NSE",
        interval: str | Interval = Interval.D1,
        n_bars: int = 10,
        fut_contract: int | None = None,
        extended_session: bool = False,
    ) -> pd.DataFrame | None:
        """Get historical data

        day light savingに注意

        Args:
            symbol (str): symbol name
            exchange (str, optional): exchange, not required if symbol is in format EXCHANGE:SYMBOL. Defaults to None.
            interval (str, optional): chart interval. Defaults to 'D'. [1,3,5,15,30,45,1H,2H,3H,4H,1D,1W,1M]
            n_bars (int, optional): no of bars to download, max 5000. Defaults to 10.
            fut_contract (int, optional): None for cash, 1 for continuous current contract in front, 2 for continuous next contract in front . Defaults to None.
            extended_session (bool, optional): regular session if False, extended session if True, Defaults to False.

        Returns:
            pd.Dataframe: dataframe with sohlcv as columns
        """
        symbol = self.__format_symbol(
            symbol=symbol, exchange=exchange, contract=fut_contract
        )

        if not isinstance(interval, str):
            interval = interval.value

        self.__create_connection()

        self.__send_message("set_auth_token", [self.token])
        self.__send_message("chart_create_session", [self.chart_session, ""])
        self.__send_message("quote_create_session", [self.session])
        self.__send_message(
            "quote_set_fields",
            [
                self.session,
                "ch",
                "chp",
                "current_session",
                "description",
                "local_description",
                "language",
                "exchange",
                "fractional",
                "is_tradable",
                "lp",
                "lp_time",
                "minmov",
                "minmove2",
                "original_name",
                "pricescale",
                "pro_name",
                "short_name",
                "type",
                "update_mode",
                "volume",
                "currency_code",
                "rchp",
                "rtc",
            ],
        )

        self.__send_message(
            "quote_add_symbols", [self.session, symbol, {"flags": ["force_permission"]}]
        )
        self.__send_message("quote_fast_symbols", [self.session, symbol])

        self.__send_message(
            "resolve_symbol",
            [
                self.chart_session,
                "symbol_1",
                '={"symbol":"'
                + symbol
                + '","adjustment":"splits","session":'
                + ('"regular"' if not extended_session else '"extended"')
                + "}",
            ],
        )
        self.__send_message(
            "create_series",
            [self.chart_session, "s1", "s1", "symbol_1", interval, n_bars],
        )
        ######### If want UTC+00, change exchange to UTC
        self.__send_message("switch_timezone", [self.chart_session, "exchange"])

        raw_data = ""

        logger.debug(f"getting data for {symbol}...")
        while True:
            try:
                if self.ws is None:
                    raise ConnectionError("WebSocket connection is None")
                result = str(self.ws.recv())  # to str
                raw_data = raw_data + result + "\n"
            except Exception as e:
                logger.error(e)
                break

            if "series_completed" in result:
                break

        return self.__create_df(raw_data, symbol)

    def search_symbol(
        self,
        text: str,
        exchange: str = "",
        search_type: str = "symbol",
        sort_country="US",
        count=50,
    ):
        """Search symbol
        Args:
            text (str): text to search
            exchange (str, optional): exchange. Defaults to "".
            search_type (str, optional): search type. Defaults to "symbol". One of
            ["stocks","undefined","funds","futures","forex",
            "crypto","bond","index","economic","options",]
        Returns:
            list: list of symbols
        """
        last_res = []
        for i in range(0, count - 50 + 1, 50):
            res: dict = self._search_symbol(
                text, exchange, search_type, start=str(i), sort_country=sort_country
            )
            if i == 0:
                print(
                    f"found: {len(res['symbols'])}",
                    "remaining: ",
                    res["symbols_remaining"],
                )
            last_res.extend(res["symbols"])

        return last_res

    def _search_symbol(
        self,
        text: str,
        exchange: str = "",
        search_type: str = "",
        sort_country="US",
        start="",
    ):
        search_types = [
            "stocks",
            "undefined",
            "funds",
            "futures",
            "forex",
            "crypto",
            "bond",
            "index",
            "economic",
            "options",
            "fundamental",
        ]
        if start != "":
            start = f"&start={start}"
        if sort_country != "":
            sort_country = f"&sort_by_country={sort_country}"
        if search_type not in search_types:
            print(ValueError("search_type must be one of {}".format(search_types)))
        url = self.__search_url.format(text, exchange, search_type, sort_country, start)
        symbols_js = {}
        try:
            resp = requests.get(url, headers=self.__search_headers)
            symbols_js = json.loads(resp.text.replace("</em>", "").replace("<em>", ""))
        except Exception as e:
            logger.exception(e)

        return symbols_js


if __name__ == "__main__":
    logging.basicConfig(level=logging.DEBUG)
    tv = TradingviewClient()
    # print(tv.get_hist("CRUDEOIL", "MCX", fut_contract=1))
    # print(tv.get_hist("NIFTY", "NSE", fut_contract=1))
    # print(
    #     tv.get_hist(
    #         "EICHERMOT",
    #         "NSE",
    #         interval=Interval.H1,
    #         n_bars=500,
    #         extended_session=False,
    #     )
    # )
    print(tv.search_symbol("PCC", "USI", "undefined"))
